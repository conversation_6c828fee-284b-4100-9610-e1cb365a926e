# 正方形循迹测试指南

## 测试前准备

### 1. 硬件检查
- [ ] 确认STM32F407开发板正常工作
- [ ] 检查电机驱动连接正确
- [ ] 验证编码器信号正常
- [ ] 确认灰度传感器阵列工作正常
- [ ] 检查BNO08x IMU传感器连接
- [ ] 确认OLED显示屏显示正常
- [ ] 检查按键功能正常

### 2. 环境准备
- [ ] 准备100cm×100cm的正方形黑线轨道
- [ ] 黑线宽度约1.8cm±0.2cm
- [ ] 在A、B、C、D四个顶点做明显标记
- [ ] 确保测试环境光线均匀
- [ ] 地面平整，无明显倾斜

### 3. 软件配置
- [ ] 编译并下载修改后的代码
- [ ] 确认系统启动正常
- [ ] 检查OLED显示信息正确

## 测试步骤

### 第一步：系统初始化测试
1. 上电启动系统
2. 观察OLED显示：
   ```
   Mode:1 Pt:0 STOP
   00000000
   Yaw:0.0 LINE
   A->B->C->D Square
   ```
3. 检查LED状态和按键响应

### 第二步：传感器功能测试
1. **灰度传感器测试**：
   - 将小车放在黑线上
   - 观察OLED第二行显示的8位二进制数
   - 应该显示黑线位置对应的传感器状态

2. **IMU传感器测试**：
   - 手动旋转小车
   - 观察OLED第三行的Yaw角度变化
   - 角度应该连续变化，无跳变

3. **编码器测试**：
   - 手动推动小车
   - 通过串口或调试信息确认编码器计数正常

### 第三步：模式选择测试
1. 按Key3切换到模式2
2. OLED应显示：`Mode:2 Pt:0 STOP`
3. 确认模式切换正常

### 第四步：循迹功能测试
1. 将小车放在A点，面向B点方向
2. 按Key1启动小车
3. 观察小车沿黑线行驶到B点

### 第五步：转向功能测试
1. 小车到达B点后应自动停止循迹
2. OLED显示应变为：`Yaw:xx.x ANGLE`
3. 小车开始右转90度
4. 转向完成后自动切换回循迹模式

### 第六步：完整循环测试
1. 让小车完成完整的A→B→C→D→A循环
2. 观察每个转向点的行为
3. 确认最终回到A点后停止

## 预期行为

### 正常运行流程
```
A点(启动) → 循迹到B点 → 右转90° → 循迹到C点 → 右转90° → 
循迹到D点 → 右转90° → 循迹到A点 → 右转90° → 停止
```

### OLED显示变化
- **循迹阶段**：`Yaw:xx.x LINE`
- **转向阶段**：`Yaw:xx.x ANGLE`
- **点计数变化**：Pt:0 → Pt:1 → ... → Pt:8

### LED指示
- **运行中**：绿色LED闪烁
- **停止**：黄色LED常亮
- **模式切换**：青色LED

## 故障排除

### 问题1：小车不能启动
- 检查`pid_running`状态
- 确认按Key1后系统响应
- 检查电机驱动连接

### 问题2：循迹不稳定
- 调整灰度传感器高度
- 检查黑线对比度
- 优化循迹PID参数

### 问题3：转向不准确
- 检查IMU传感器校准
- 调整角度PID参数
- 确认角度到达检测逻辑

### 问题4：无法检测到转向点
- 检查灰度传感器全黑检测
- 确认转向点标记清晰
- 调整检测阈值

### 问题5：角度累积错误
- 重新校准IMU传感器
- 检查连续角度转换函数
- 重置角度系统

## 参数调优

### 循迹PID参数 (如需调整)
```c
PidParams_t pid_params_line = {
    .kp = 8.25f,    // 增加：响应更快，减少：更稳定
    .ki = 0.0f,     // 通常保持为0
    .kd = 0.0f,     // 可适当增加减少震荡
};
```

### 角度PID参数 (如需调整)
```c
PidParams_t pid_params_angle = {
    .kp = 2.5f,     // 增加：转向更快，减少：转向更稳
    .ki = 0.01f,    // 消除稳态误差
    .kd = 0.1f,     // 减少超调
};
```

### 速度参数
- 循迹速度：60 cm/s (可调整basic_speed)
- 转向速度：70 cm/s
- 角度精度：±3度
- 稳定时间：50ms (10次检测)

## 成功标准

- [ ] 小车能稳定沿黑线循迹
- [ ] 在四个转向点准确停止并转向
- [ ] 每次转向角度误差小于±5度
- [ ] 完整循环时间合理（建议2-3分钟）
- [ ] 最终能准确回到起始点A
- [ ] 系统运行稳定，无异常停止

## 注意事项

1. 首次测试建议降低速度参数
2. 确保电池电量充足
3. 测试过程中随时准备按Key4紧急停止
4. 记录测试过程中的参数调整
5. 建议多次测试验证稳定性
