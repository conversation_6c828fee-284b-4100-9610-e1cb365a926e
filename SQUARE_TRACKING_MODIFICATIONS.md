# 正方形循迹代码修改说明

## 修改目标
根据图片要求，让小车按照A→B→C→D的正方形路径进行循迹，不改变小车运行方向。

## 主要修改内容

### 1. 状态机逻辑修改 (User/my_timer.c)

修改了`Car_State_Update()`函数中的case 2，实现8个状态的正方形循迹：

```c
case 2: // 正方形循迹 A -> B -> C -> D
  if(point_count == 1) // 到达B点，准备右转90度
  {
    pid_control_mode = 0; // 切换到角度控制模式
    pid_set_target(&pid_angle, 90); // 设置目标角度：右转90度
  }
  else if(point_count == 2) // B->C转向完成，开始循迹到C点
  {
    pid_control_mode = 1; // 切换到循迹控制模式
  }
  // ... 继续其他状态
```

**状态流程**：
- point_count = 1: 到达B点，开始右转90度
- point_count = 2: 转向完成，开始B→C循迹
- point_count = 3: 到达C点，开始右转90度
- point_count = 4: 转向完成，开始C→D循迹
- point_count = 5: 到达D点，开始右转90度
- point_count = 6: 转向完成，开始D→A循迹
- point_count = 7: 到达A点，开始右转90度
- point_count = 8: 完成一圈，停止小车

### 2. 角度控制优化 (User/App/pid_app.c)

#### 2.1 PID参数调整
```c
PidParams_t pid_params_angle = {
    .kp = 2.5f,        // 增加比例系数，提高响应速度
    .ki = 0.01f,       // 添加少量积分，消除稳态误差
    .kd = 0.1f,        // 添加微分，减少超调
    .out_min = -50.0f, // 限制输出范围，避免转向过激
    .out_max = 50.0f,
};
```

#### 2.2 角度到达检测
```c
// 检查是否到达目标角度（允许±3度误差）
static uint8_t angle_stable_count = 0;

if(fabs(pid_angle.error) < 3.0f && system_mode == 2)
{
  // 需要连续几次检测都在误差范围内才认为到达
  if(++angle_stable_count >= 10) // 约50ms稳定时间
  {
    point_count++;
    angle_stable_count = 0;
    pid_reset(&pid_angle);
  }
}
```

### 3. 显示界面优化 (User/App/oled_app.c)

增强OLED显示信息：
```c
oled_printf(0, 0, "Mode:%d Pt:%d %s", system_mode, point_count, pid_running ? "RUN" : "STOP");
oled_printf(0, 1, "%d%d%d%d%d%d%d%d", /* 灰度传感器状态 */);
oled_printf(0, 2, "Yaw:%.1f %s", yaw, pid_control_mode ? "LINE" : "ANGLE");
oled_printf(0, 3, "A->B->C->D Square");
```

### 4. 按键功能增强 (User/App/key_app.c)

#### 4.1 模式切换优化 (Key3)
- 增加系统状态重置功能
- 重置角度系统和PID控制器
- 重置点计数器

#### 4.2 紧急停止功能 (Key4)
- 立即停止小车运行
- 设置黄色LED指示停止状态

## 使用说明

### 操作步骤
1. **Key3**: 切换到模式2（正方形循迹模式）
2. **Key1**: 启动小车开始循迹
3. **Key4**: 紧急停止小车
4. **Key3**: 重新选择模式

### 运行逻辑
1. 小车从A点开始，沿黑线循迹到B点
2. 到达B点后，切换到角度控制，右转90度
3. 转向完成后，切换回循迹控制，沿黑线到C点
4. 重复上述过程，依次经过C→D→A
5. 完成整个正方形后自动停止

### 关键参数
- **循迹速度**: 60 cm/s
- **转向速度**: 70 cm/s
- **角度精度**: ±3度
- **稳定时间**: 50ms

## 技术特点

1. **双环控制**: 内环速度控制 + 外环循迹/角度控制
2. **智能切换**: 循迹模式与角度控制模式自动切换
3. **精确转向**: 连续角度处理，避免角度跳变
4. **稳定检测**: 角度到达需要连续稳定才确认
5. **实时显示**: OLED显示当前状态和调试信息

## 注意事项

1. 确保灰度传感器正确校准
2. 确保BNO08x IMU传感器正常工作
3. 黑线宽度应适合灰度传感器阵列
4. 正方形的四个角应有明确的标识点
5. 建议在平坦、光线均匀的环境中测试
