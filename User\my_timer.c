#include "my_timer.h"

extern int basic_speed;

// 状态机实例
StateMachine_t car_fsm = {
    .current_state = STATE_IDLE,
    .next_state = STATE_IDLE,
    .corner_count = 0,
    .target_corners = 4,  // 正方形需要4个转角
    .target_angle = 0,
    .state_enter_time = 0,
    .timeout_ms = 5000    // 5秒超时
};

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int output_timer500ms = 0;

unsigned char intput_ff_flag;
unsigned int intput_timer500ms;

unsigned int led_timer500ms; // ÿ����һ���㣬LED ���� 500ms ��Ϩ��

unsigned char point_count = 0; // �����ĵ�λ��������Ȧ + 1����Ȧ + 1��

unsigned char system_mode = 1; // ϵͳ״̬��1 ~ 4 ��Ӧ 4 ����Ŀ��

unsigned char circle_count = 0; // �������Ȧ��������

unsigned int distance = 0; // ��¼С��ÿһ����ʻ�ľ���

extern uint8_t led_rgb[5];

void timer_init(void)
{
  HAL_TIM_Base_Start_IT(&htim2);
  state_machine_init();
}

// 状态机初始化
void state_machine_init(void)
{
    car_fsm.current_state = STATE_IDLE;
    car_fsm.next_state = STATE_IDLE;
    car_fsm.corner_count = 0;
    car_fsm.target_corners = 4;
    car_fsm.target_angle = 0;
    car_fsm.state_enter_time = HAL_GetTick();
    car_fsm.timeout_ms = 5000;
}

// 改进的状态机更新函数
void state_machine_update(void)
{
    uint32_t current_time = HAL_GetTick();
    float current_yaw = get_yaw();

    // 检查超时
    if(current_time - car_fsm.state_enter_time > car_fsm.timeout_ms)
    {
        // 超时处理，切换到错误状态
        car_fsm.current_state = STATE_ERROR;
        stop_flat = 1;
        return;
    }

    switch(car_fsm.current_state)
    {
        case STATE_IDLE:
            if(pid_running)
            {
                car_fsm.next_state = STATE_LINE_TRACKING;
                pid_control_mode = 1; // 循迹模式
            }
            break;

        case STATE_LINE_TRACKING:
            // 检测是否到达转角点
            if(Digtal == 0x00) // 全黑检测
            {
                car_fsm.next_state = STATE_TURNING;
                car_fsm.target_angle = current_yaw + 90; // 右转90度
                pid_control_mode = 0; // 角度控制模式
                pid_set_target(&pid_angle, car_fsm.target_angle);
            }
            break;

        case STATE_TURNING:
            // 检查是否到达目标角度
            if(fabs(current_yaw - car_fsm.target_angle) < 3.0f)
            {
                car_fsm.next_state = STATE_TURN_COMPLETE;
                car_fsm.corner_count++;
            }
            break;

        case STATE_TURN_COMPLETE:
            // 转向完成，继续循迹或结束任务
            if(car_fsm.corner_count >= car_fsm.target_corners)
            {
                car_fsm.next_state = STATE_MISSION_COMPLETE;
                stop_flat = 1;
            }
            else
            {
                car_fsm.next_state = STATE_LINE_TRACKING;
                pid_control_mode = 1; // 循迹模式
            }
            break;

        case STATE_MISSION_COMPLETE:
            // 任务完成，保持停止状态
            stop_flat = 1;
            pid_running = 0;
            break;

        case STATE_ERROR:
            // 错误状态，停止所有动作
            stop_flat = 1;
            pid_running = 0;
            break;
    }

    // 状态切换
    if(car_fsm.next_state != car_fsm.current_state)
    {
        car_fsm.current_state = car_fsm.next_state;
        car_fsm.state_enter_time = current_time;

        // 重置PID控制器
        pid_reset(&pid_line);
        pid_reset(&pid_angle);
    }
}

// TIM2 �жϷ�������1ms �жϣ�
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == htim2.Instance)
	{
		if(++measure_timer5ms >= 5)
		{
			measure_timer5ms = 0;
			
			Encoder_Task();
			distance += left_encoder.speed_cm_s;
			Gray_Task();
			bno080_task();

			// 更新状态机
			state_machine_update();

			PID_Task();
		}
		
//		if(pid_running != 1) return;
		
		/* ��Ȧ������ */
		if(Digtal != 0x00)
		{
			output_ff_flag = 1;
			if(++intput_timer500ms >= 500) intput_timer500ms = 500;
		}
		else if(output_ff_flag == 1 && intput_timer500ms == 500)
		{
			output_ff_flag = 0;
			intput_timer500ms = 0;
			point_count++;
			Car_State_Update();
		}
////		
////	  /* ��Ȧ������ */
	  if(Digtal == 0x00)
	  {
	    intput_ff_flag = 1;
	    if(++output_timer500ms >= 500) output_timer500ms = 500;
	  }
	  else if(intput_ff_flag == 1 && output_timer500ms == 500)
	  {
	    intput_ff_flag = 0;
	    output_timer500ms = 0;
	    point_count++;
	    Car_State_Update();
	  }
		
		
		/* LED ״̬��� */
	  if(led_rgb[0] == 1 && ++led_timer500ms >= 500)
	  {
	    led_rgb[0] = 0;
	    led_timer500ms = 0;
	  }
	}
}

extern uint8_t stop_flat;
//ÿ�ε�λ��������ʱ������ϵͳ״̬ͬ������С����״̬
void Car_State_Update(void)
{
  led_rgb[0] = 1;
  distance = 0;
  
  switch(system_mode)
  {
    case 1: // ��һ�⣺ֱ����ʻ A -> B
      if(point_count == 1)
      {
//        pid_running = 0;
////        motor_break();
				point_count = 0;
				stop_flat = 1;
      }
      break;
    case 2: // 正方形循迹 A -> B -> C -> D
      if(point_count == 1) // 到达B点，准备右转90度
      {
        pid_control_mode = 0; // 切换到角度控制模式
        pid_set_target(&pid_angle, 90); // 设置目标角度：右转90度
      }
      else if(point_count == 2) // B->C转向完成，开始循迹到C点
      {
        pid_control_mode = 1; // 切换到循迹控制模式
      }
      else if(point_count == 3) // 到达C点，准备右转90度
      {
        pid_control_mode = 0; // 切换到角度控制模式
        pid_set_target(&pid_angle, 180); // 设置目标角度：累计180度
      }
      else if(point_count == 4) // C->D转向完成，开始循迹到D点
      {
        pid_control_mode = 1; // 切换到循迹控制模式
      }
      else if(point_count == 5) // 到达D点，准备右转90度
      {
        pid_control_mode = 0; // 切换到角度控制模式
        pid_set_target(&pid_angle, 270); // 设置目标角度：累计270度
      }
      else if(point_count == 6) // D->A转向完成，开始循迹到A点
      {
        pid_control_mode = 1; // 切换到循迹控制模式
      }
      else if(point_count == 7) // 到达A点，准备右转90度回到起始方向
      {
        pid_control_mode = 0; // 切换到角度控制模式
        pid_set_target(&pid_angle, 360); // 设置目标角度：累计360度
      }
      else if(point_count == 8) // 完成整个正方形循迹
      {
        stop_flat = 1; // 停止小车
        point_count = 0; // 重置计数器
      }
      break;
    case 3: // �����⣺8 �ֻ���һȦ A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1;
      }
      break;
    case 4: // �����⣺8 �ֻ�����Ȧ
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 4)
      {
				pid_set_target(&pid_angle, 36);
        if(++circle_count >= 4)
        {
//          pid_running = 0;
//          motor_break();
					stop_flat = 1;
        }
        point_count = 0;
        pid_control_mode = 0; // ʹ�ýǶȻ�����
      }
      break;
  }
  
  /* ������ʷ��� */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
