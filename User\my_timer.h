#ifndef __MY_TIMER_H__
#define __MY_TIMER_H__

#include "mydefine.h"

// 状态机枚举定义
typedef enum {
    STATE_IDLE = 0,
    STATE_LINE_TRACKING,
    STATE_TURNING,
    STATE_TURN_COMPLETE,
    STATE_MISSION_COMPLETE,
    STATE_ERROR
} CarState_t;

// 转向方向枚举
typedef enum {
    TURN_RIGHT_90 = 90,
    TURN_LEFT_90 = -90,
    TURN_180 = 180
} TurnDirection_t;

// 状态机结构体
typedef struct {
    CarState_t current_state;
    CarState_t next_state;
    uint8_t corner_count;        // 当前转过的角数
    uint8_t target_corners;      // 目标角数
    float target_angle;          // 目标角度
    uint32_t state_enter_time;   // 状态进入时间
    uint32_t timeout_ms;         // 状态超时时间
} StateMachine_t;

void timer_init(void);
void Car_State_Update(void);
void time_run(void);
void state_machine_init(void);
void state_machine_update(void);

extern unsigned char point_count;
extern unsigned char system_mode;
extern unsigned int distance;
extern StateMachine_t car_fsm;

#endif
