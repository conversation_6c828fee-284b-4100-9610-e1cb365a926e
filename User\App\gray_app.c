#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};

float g_line_position_error;

// 路径预测器实例
PathPredictor_t path_predictor = {
    .position_history = {0},
    .velocity_estimate = 0,
    .predicted_position = 0,
    .confidence = 0,
    .history_index = 0
};

// 线路检测实例
LineDetection_t line_detection = {
    .sensor_data = 0,
    .position_error = 0,
    .line_width = 0,
    .line_detected = 0,
    .intersection_detected = 0,
    .corner_detected = 0
};

void Gray_Init(void)
{
    // 测试软件I2C连接
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");
    }
}

void Gray_Task(void)
{
//		HAL_NVIC_DisableIRQ(TIM2_IRQn);
		uint8_t temp = 0;
		temp = IIC_Get_Digtal();
		if(temp == 0xAA)
		{
			return;
		}
    Digtal=~temp;
	
//		HAL_NVIC_EnableIRQ(TIM2_IRQn); 
		
//    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
    {
      g_line_position_error = weighted_sum / (float)black_line_count;

      // 更新线路检测
      line_detection_update(&line_detection, Digtal);

      // 更新路径预测
      path_predictor_update(&path_predictor, g_line_position_error);
    }
}

// 路径预测器更新
void path_predictor_update(PathPredictor_t* predictor, float current_position)
{
    // 更新位置历史
    predictor->position_history[predictor->history_index] = current_position;
    predictor->history_index = (predictor->history_index + 1) % 5;

    // 计算速度估计（位置变化率）
    float pos_diff = predictor->position_history[0] - predictor->position_history[4];
    predictor->velocity_estimate = pos_diff / 5.0f; // 5个采样周期的平均变化率

    // 预测下一个位置
    predictor->predicted_position = current_position + predictor->velocity_estimate * 2.0f;

    // 计算预测置信度
    float variance = 0;
    float mean = 0;
    for(int i = 0; i < 5; i++)
    {
        mean += predictor->position_history[i];
    }
    mean /= 5.0f;

    for(int i = 0; i < 5; i++)
    {
        float diff = predictor->position_history[i] - mean;
        variance += diff * diff;
    }
    variance /= 5.0f;

    // 方差越小，置信度越高
    predictor->confidence = 1.0f / (1.0f + variance);
}

// 线路检测更新
void line_detection_update(LineDetection_t* detection, uint8_t sensor_data)
{
    detection->sensor_data = sensor_data;

    // 计算线宽
    uint8_t line_count = 0;
    for(int i = 0; i < 8; i++)
    {
        if((sensor_data >> i) & 0x01)
            line_count++;
    }
    detection->line_width = (float)line_count;

    // 线路检测
    detection->line_detected = (line_count > 0) ? 1 : 0;

    // 交叉点检测（线宽突然增加）
    static float last_line_width = 0;
    if(detection->line_width > last_line_width + 3)
    {
        detection->intersection_detected = 1;
    }
    else
    {
        detection->intersection_detected = 0;
    }
    last_line_width = detection->line_width;

    // 转角检测（全黑或全白）
    if(sensor_data == 0xFF || sensor_data == 0x00)
    {
        detection->corner_detected = 1;
    }
    else
    {
        detection->corner_detected = 0;
    }
}

// 获取预测误差
float get_predicted_error(void)
{
    if(path_predictor.confidence > 0.7f)
    {
        return path_predictor.predicted_position;
    }
    else
    {
        return g_line_position_error; // 置信度低时使用当前误差
    }
}
