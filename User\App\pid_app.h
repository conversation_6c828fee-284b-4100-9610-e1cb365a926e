#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "mydefine.h"
#include "pid.h"

// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;

// 自适应PID控制器
typedef struct
{
    PidParams_t base_params;     // 基础参数
    PidParams_t current_params;  // 当前参数
    float error_threshold_low;   // 低误差阈值
    float error_threshold_high;  // 高误差阈值
    float adaptation_rate;       // 自适应速率
    uint8_t adaptation_enabled;  // 自适应使能
} AdaptivePID_t;

// 智能速度控制结构体
typedef struct
{
    float base_speed;           // 基础速度
    float current_speed;        // 当前速度
    float max_speed;            // 最大速度
    float min_speed;            // 最小速度
    float curvature_factor;     // 曲率因子
    float acceleration_limit;   // 加速度限制
    uint8_t adaptive_enabled;   // 自适应使能
} SmartSpeedControl_t;

void PID_Init(void);
void PID_Task(void);
void adaptive_pid_update(AdaptivePID_t* apid, float error);
void smart_speed_update(SmartSpeedControl_t* ssc, float line_error);
float calculate_curvature(float line_error, float last_error);

extern bool pid_running; // PID ����ʹ�ܿ���
extern unsigned char pid_control_mode;

extern int basic_speed;

extern PID_T pid_speed_left;  // �����ٶȻ�
extern PID_T pid_speed_right; // �����ٶȻ�

extern PID_T pid_line;        // ѭ����

extern PID_T pid_angle;       // �ǶȻ�

#endif
