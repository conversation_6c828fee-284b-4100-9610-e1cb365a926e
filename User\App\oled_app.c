#include "oled_app.h"
#include "oled.h"

//extern I2C_HandleTypeDef hi2c2;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}


void my_oled_init(void)
{
	OLED_Init();
}	

extern unsigned char system_mode;
extern unsigned char point_count;
extern unsigned char pid_control_mode;
extern bool pid_running;

void oled_task(void)
{
	float yaw = get_yaw();
  oled_printf(0, 0, "Mode:%d Pt:%d %s", system_mode, point_count, pid_running ? "RUN" : "STOP");
	oled_printf(0, 1, "%d%d%d%d%d%d%d%d", (Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
	oled_printf(0, 2, "Yaw:%.1f %s", yaw, pid_control_mode ? "LINE" : "ANGLE");
	oled_printf(0, 3, "A->B->C->D Square");
}

