#include "oled_app.h"
#include "oled.h"

//extern I2C_HandleTypeDef hi2c2;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}


void my_oled_init(void)
{
	OLED_Init();
}	

extern unsigned char system_mode;
extern unsigned char point_count;
extern unsigned char pid_control_mode;
extern bool pid_running;
extern StateMachine_t car_fsm;
extern PathPredictor_t path_predictor;
extern LineDetection_t line_detection;

// 状态名称字符串
const char* state_names[] = {
    "IDLE", "TRACK", "TURN", "DONE", "COMP", "ERR"
};

void oled_task(void)
{
	float yaw = get_yaw();

	// 第一行：模式、状态、转角计数
  oled_printf(0, 0, "M%d %s C%d", system_mode,
              state_names[car_fsm.current_state], car_fsm.corner_count);

  // 第二行：传感器状态和线宽
	oled_printf(0, 1, "%d%d%d%d%d%d%d%d W%.1f",
	            (Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,
	            (Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01,
	            line_detection.line_width);

	// 第三行：角度和控制模式
	oled_printf(0, 2, "Y%.1f %s", yaw, pid_control_mode ? "LINE" : "ANGL");

	// 第四行：预测信息和置信度
	oled_printf(0, 3, "P%.1f C%.2f", path_predictor.predicted_position,
	            path_predictor.confidence);
}

