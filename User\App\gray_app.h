#ifndef __GRAY_APP_H
#define __GRAY_APP_H

#include "mydefine.h"

// 路径预测结构体
typedef struct
{
    float position_history[5];     // 位置历史
    float velocity_estimate;       // 速度估计
    float predicted_position;      // 预测位置
    float confidence;              // 预测置信度
    uint8_t history_index;         // 历史索引
} PathPredictor_t;

// 线路检测结构体
typedef struct
{
    uint8_t sensor_data;           // 传感器原始数据
    float position_error;          // 位置误差
    float line_width;              // 线宽估计
    uint8_t line_detected;         // 线路检测标志
    uint8_t intersection_detected; // 交叉点检测
    uint8_t corner_detected;       // 转角检测
} LineDetection_t;

void Gray_Init(void);
void Gray_Task(void);
void path_predictor_update(PathPredictor_t* predictor, float current_position);
void line_detection_update(LineDetection_t* detection, uint8_t sensor_data);
float get_predicted_error(void);

extern unsigned char Digtal;
extern float g_line_position_error; // 循迹偏差值
extern PathPredictor_t path_predictor;
extern LineDetection_t line_detection;

#endif
